import pandas as pd
import re
from datetime import datetime

def parse_date_range(date_str, year):
    """
    Phân tích chuỗi ngày tháng và trả về start_date, end_date
    Ví dụ: "01.17-01.19" -> ('2024-01-17', '2024-01-19')
    """
    if pd.isna(date_str) or not date_str:
        return None, None
    
    date_str = str(date_str).strip()
    
    # Xử lý các format khác nhau
    # Format: MM.DD-MM.DD
    pattern1 = r'(\d{1,2})\.(\d{1,2})-(\d{1,2})\.(\d{1,2})'
    match1 = re.match(pattern1, date_str)
    if match1:
        start_month, start_day, end_month, end_day = match1.groups()
        start_date = f"{year}-{start_month.zfill(2)}-{start_day.zfill(2)}"
        end_date = f"{year}-{end_month.zfill(2)}-{end_day.zfill(2)}"
        return start_date, end_date
    
    # Format: MM.DD-DD (cùng tháng)
    pattern2 = r'(\d{1,2})\.(\d{1,2})-(\d{1,2})'
    match2 = re.match(pattern2, date_str)
    if match2:
        month, start_day, end_day = match2.groups()
        start_date = f"{year}-{month.zfill(2)}-{start_day.zfill(2)}"
        end_date = f"{year}-{month.zfill(2)}-{end_day.zfill(2)}"
        return start_date, end_date
    
    # Format: YYYY/MM/DD~DD hoặc YYYY.MM.DD-DD
    pattern3 = r'(\d{4})[/.](\d{1,2})[/.](\d{1,2})[~-](\d{1,2})'
    match3 = re.match(pattern3, date_str)
    if match3:
        year_found, month, start_day, end_day = match3.groups()
        start_date = f"{year_found}-{month.zfill(2)}-{start_day.zfill(2)}"
        end_date = f"{year_found}-{month.zfill(2)}-{end_day.zfill(2)}"
        return start_date, end_date
    
    # Format: YYYY/MM/DD~YYYY/MM/DD
    pattern4 = r'(\d{4})[/.](\d{1,2})[/.](\d{1,2})[~-](\d{4})[/.](\d{1,2})[/.](\d{1,2})'
    match4 = re.match(pattern4, date_str)
    if match4:
        start_year, start_month, start_day, end_year, end_month, end_day = match4.groups()
        start_date = f"{start_year}-{start_month.zfill(2)}-{start_day.zfill(2)}"
        end_date = f"{end_year}-{end_month.zfill(2)}-{end_day.zfill(2)}"
        return start_date, end_date
    
    # Format: MM/DD~MM/DD
    pattern5 = r'(\d{1,2})/(\d{1,2})~(\d{1,2})/(\d{1,2})'
    match5 = re.match(pattern5, date_str)
    if match5:
        start_month, start_day, end_month, end_day = match5.groups()
        start_date = f"{year}-{start_month.zfill(2)}-{start_day.zfill(2)}"
        end_date = f"{year}-{end_month.zfill(2)}-{end_day.zfill(2)}"
        return start_date, end_date
    
    print(f"Không thể phân tích ngày: {date_str}")
    return None, None

def clean_text(text):
    """Làm sạch text và escape ký tự đặc biệt cho SQL"""
    if pd.isna(text) or not text:
        return ""
    
    text = str(text).strip()
    # Escape single quotes cho SQL
    text = text.replace("'", "''")
    return text

def generate_sql_for_year(year_str, file_path):
    """Tạo file SQL cho một năm cụ thể"""
    
    # Bỏ qua các năm đã có
    if year_str in ['2024', '2023', '2022']:
        print(f"Bỏ qua năm {year_str} - đã có file SQL")
        return
    
    try:
        # Đọc sheet tương ứng
        df = pd.read_excel(file_path, sheet_name=year_str)
        
        # Tìm dòng header
        header_row = None
        for idx, row in df.iterrows():
            if any('전시회' in str(cell) or '개최도시' in str(cell) for cell in row if pd.notna(cell)):
                header_row = idx
                break
        
        if header_row is not None:
            # Đọc lại với header đúng
            df = pd.read_excel(file_path, sheet_name=year_str, header=header_row)
        
        # Loại bỏ dòng trống
        df = df.dropna(how='all')
        
        # Xác định năm để parse ngày
        if '-' in year_str:
            # Trường hợp "2006-1997", sử dụng năm đầu tiên
            year_for_parsing = year_str.split('-')[0]
        else:
            year_for_parsing = year_str
        
        sql_statements = []
        
        for idx, row in df.iterrows():
            # Bỏ qua dòng header nếu còn sót lại
            if any('전시회' in str(cell) or '개최도시' in str(cell) for cell in row if pd.notna(cell)):
                continue
            
            # Lấy dữ liệu từ các cột
            date_info = clean_text(row.iloc[0]) if len(row) > 0 else ""
            location = clean_text(row.iloc[1]) if len(row) > 1 else ""
            event_name = clean_text(row.iloc[2]) if len(row) > 2 else ""
            
            # Bỏ qua dòng trống
            if not date_info and not location and not event_name:
                continue
            
            # Parse ngày tháng
            start_date, end_date = parse_date_range(date_info, year_for_parsing)
            
            if start_date and end_date and event_name and location:
                # Tạo câu SQL INSERT
                sql = f"(N'{event_name}', '{start_date}', '{end_date}', N'{location}', N'{location}')"
                sql_statements.append(sql)
        
        if sql_statements:
            # Tạo file SQL
            filename = f"insert_history_{year_str.replace('-', '_')}.sql"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("INSERT INTO tbl_history (name_kr, start, end, location_kr, location_en) VALUES\n")
                
                for i, sql in enumerate(sql_statements):
                    if i == len(sql_statements) - 1:
                        f.write(sql + ";\n")
                    else:
                        f.write(sql + ",\n")
            
            print(f"✅ Đã tạo file {filename} với {len(sql_statements)} bản ghi")
        else:
            print(f"❌ Không có dữ liệu hợp lệ cho năm {year_str}")
            
    except Exception as e:
        print(f"❌ Lỗi khi xử lý năm {year_str}: {e}")

def main():
    file_path = '코이코 연혁표 1997-2024.xlsx'
    
    try:
        # Đọc danh sách các sheet
        excel_file = pd.ExcelFile(file_path)
        
        print("=== TẠO CÁC FILE SQL CHO DỮ LIỆU LỊCH SỬ ===")
        print(f"File Excel: {file_path}")
        print(f"Tổng số sheet: {len(excel_file.sheet_names)}")
        
        # Xử lý từng sheet (bỏ qua 2024, 2023, 2022)
        for sheet_name in excel_file.sheet_names:
            if sheet_name not in ['2024', '2023', '2022']:
                print(f"\n--- Xử lý sheet: {sheet_name} ---")
                generate_sql_for_year(sheet_name, file_path)
        
        print("\n=== HOÀN THÀNH ===")
        print("Các file SQL đã được tạo thành công!")
        
    except Exception as e:
        print(f"Lỗi: {e}")

if __name__ == "__main__":
    main()
