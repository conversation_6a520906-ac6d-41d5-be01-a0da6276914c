import pandas as pd
import sys

try:
    # Đọc file Excel
    file_path = '코이코 연혁표 1997-2024.xlsx'

    # Thử đọc tất cả các sheet
    excel_file = pd.ExcelFile(file_path)
    print('=== THÔNG TIN TỔNG QUAN VỀ FILE EXCEL ===')
    print(f'File: {file_path}')
    print(f'Số lượng sheet: {len(excel_file.sheet_names)}')
    print('\nCác sheet trong file Excel:')
    for i, sheet_name in enumerate(excel_file.sheet_names, 1):
        print(f'{i:2d}. {sheet_name}')

    print('\n' + '='*60)
    print('=== PHÂN TÍCH CHI TIẾT TỪNG SHEET ===')

    total_records = 0

    # Phân tích từng sheet
    for sheet_name in excel_file.sheet_names:
        print(f'\n--- SHEET: {sheet_name} ---')
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # <PERSON>ại bỏ các dòng trống
            df_clean = df.dropna(how='all')

            print(f'Số dòng: {len(df)} (gốc), {len(df_clean)} (sau khi loại bỏ dòng trống)')
            print(f'Số cột: {len(df.columns)}')

            if len(df_clean) > 0:
                total_records += len(df_clean)

                # Tìm dòng header (thường là dòng có chứa "전시회", "개최도시", etc.)
                header_row = None
                for idx, row in df.iterrows():
                    if any('전시회' in str(cell) or '개최도시' in str(cell) for cell in row if pd.notna(cell)):
                        header_row = idx
                        break

                if header_row is not None:
                    print(f'Dòng header tìm thấy ở vị trí: {header_row}')
                    # Đọc lại với header đúng
                    df_with_header = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
                    df_with_header = df_with_header.dropna(how='all')

                    print(f'Tên các cột sau khi xác định header:')
                    for col in df_with_header.columns:
                        print(f'  - {col}')

                    # Hiển thị vài dòng dữ liệu mẫu
                    if len(df_with_header) > 0:
                        print(f'\nVài dòng dữ liệu mẫu (tối đa 5 dòng):')
                        sample_data = df_with_header.head(5)
                        for idx, row in sample_data.iterrows():
                            print(f'  {idx}: {" | ".join([str(val)[:30] + "..." if len(str(val)) > 30 else str(val) for val in row if pd.notna(val)])}')
                else:
                    print('Không tìm thấy dòng header rõ ràng')
                    print('Vài dòng đầu tiên:')
                    for idx, row in df_clean.head(3).iterrows():
                        print(f'  {idx}: {" | ".join([str(val)[:30] + "..." if len(str(val)) > 30 else str(val) for val in row if pd.notna(val)])}')
            else:
                print('Sheet này không có dữ liệu')

        except Exception as e:
            print(f'Lỗi khi đọc sheet {sheet_name}: {e}')

    print('\n' + '='*60)
    print('=== TÓM TẮT ===')
    print(f'Tổng số sheet: {len(excel_file.sheet_names)}')
    print(f'Các năm có dữ liệu: {", ".join(excel_file.sheet_names)}')
    print(f'Khoảng thời gian: Từ 1997 đến 2024 (28 năm)')
    print(f'Tổng số bản ghi ước tính: {total_records}')
    print('\nNội dung chính: Có vẻ như đây là bảng lịch sử các triển lãm/hội chợ')
    print('với thông tin về ngày tháng, địa điểm và tên sự kiện.')

except Exception as e:
    print(f'Lỗi khi đọc file: {e}')
    print('Có thể cần cài đặt thư viện pandas và openpyxl')
